/*
 * Copyright (C) 2008-2014 TrinityCore <http://www.trinitycore.org/>
 *
 * This program is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License as published by the
 * Free Software Foundation; either version 2 of the License, or (at your
 * option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
 * more details.
 *
 * You should have received a copy of the GNU General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

#ifndef TRINITY_SCENARIO_H
#define TRINITY_SCENARIO_H

// 包含必要的头文件和依赖声明
#include "Common.h"          // 通用定义和基础类型
#include "AchievementMgr.h"  // 成就管理器模板类
#include "Challenge.h"       // 挑战模式相关定义
#include <safe_ptr.h>        // 线程安全智能指针

// 前向声明：挑战模式类
class Challenge;

// 前向声明：场景步骤条目结构体（来自DB2数据）
struct ScenarioStepEntry;

// 类型定义：场景步骤列表，存储场景中所有步骤的有序集合
typedef std::vector<ScenarioStepEntry const*> ScenarioSteps;

// 网络数据包命名空间：包含场景相关的网络通信结构
namespace WorldPackets
 {
     namespace Scenario
     {
         // 前向声明：奖励目标数据结构体
         struct BonusObjectiveData;
     }
 }

// LFG（寻找队伍）系统命名空间
namespace lfg
{
    // 前向声明：LFG副本数据结构体
    struct LFGDungeonData;
}

// 场景类型枚举 - 定义不同类型的游戏场景（对应Scenario.db2数据库表）
enum ScenarioType	//Scenario.db2 ScenarioEntry
{
    SCENARIO_TYPE_DEFAULT               = 0,	// 默认场景类型 - 普通的剧情任务、教程场景或一般性游戏事件
    SCENARIO_TYPE_CHALLENGE_MODE        = 1,	// 挑战模式场景类型 - 大秘境（Mythic Keystone）系统，具有时间限制和词缀效果
    SCENARIO_TYPE_PROVING_GROUNDS       = 2,	// 试炼场场景类型 - 玩家单独进行战斗技能测试的特殊场景
    SCENARIO_TYPE_USE_DUNGEON_DISPLAY   = 3,	// 使用副本显示场景类型 - 采用副本UI界面显示的特殊场景
    SCENARIO_TYPE_LEGION_INVASION       = 4,	// 军团入侵场景类型 - 7.0版本军团再临资料片的世界入侵事件场景
    SCENARIO_TYPE_BOOST_TUTORIAL        = 5,	// 快速提升教程场景类型 - 角色等级提升服务的引导教程场景
};

// 场景步骤状态枚举 - 定义场景中每个步骤的执行状态
enum ScenarioStepState
{
    SCENARIO_STEP_INVALID       = 0,    // 无效状态 - 步骤数据无效或未正确初始化
    SCENARIO_STEP_NOT_STARTED   = 1,    // 未开始状态 - 步骤尚未激活，等待前置条件满足
    SCENARIO_STEP_IN_PROGRESS   = 2,    // 进行中状态 - 步骤正在执行，玩家可以完成相关目标
    SCENARIO_STEP_DONE          = 3     // 已完成状态 - 步骤目标已达成，可以推进到下一步骤
};

// 场景类 - 管理游戏中各种场景的核心控制器，包括普通场景、挑战模式、户外PvP等
class Scenario
{
public:
    // 构造函数1：基于LFG副本数据创建场景对象
    // 参数：map-地图指针，_dungeonData-LFG副本数据，player-玩家指针，find-是否查找现有场景
    Scenario(Map* map, lfg::LFGDungeonData const* _dungeonData, Player* player, bool find);

    // 构造函数2：基于场景ID创建场景对象（简化版本）
    // 参数：map-地图指针，scenarioID-场景ID
    Scenario(Map* map, uint32 scenarioID);

    // 析构函数：清理场景资源，包括挑战模式对象和全局计数器
    ~Scenario();

    // 场景更新函数：处理场景的定时更新逻辑
    // 参数：t_diff-时间差值（毫秒），用于计算经过的时间
    void Update(const uint32 t_diff);

    // 创建挑战模式函数：为大秘境创建挑战对象和相关配置
    // 参数：player-触发挑战模式的玩家指针
    void CreateChallenge(Player* player);       //创建大秘境挑战

    // 获取实例ID：返回当前场景所在的地图实例ID
    uint32 GetInstanceId() const;

    // 获取地图对象：返回场景关联的地图指针
    Map* GetMap();

    // 获取户外PvP对象：返回户外PvP场景的管理对象指针
    OutdoorPvP* GetOutdoorPvP();

    // 获取场景ID：返回当前场景的唯一标识符
    uint32 GetScenarioId() const;

    // 获取当前步骤：返回场景当前执行的步骤索引
    uint32 GetCurrentStep() const;

    // 获取户外PvP区域ID：返回户外PvP场景的区域标识符
    uint32 GetOutdoorPvPZone() const;

    // 设置步骤状态：更新指定场景步骤的执行状态
    // 参数：step-步骤条目指针，state-新的步骤状态
    void SetStepState(ScenarioStepEntry const* step, ScenarioStepState state);

    // 获取步骤状态：查询指定场景步骤的当前执行状态
    // 参数：step-步骤条目指针，返回值：步骤的当前状态
    ScenarioStepState GetStepState(ScenarioStepEntry const* step);

    // 检查场景完成状态：判断场景是否已完成（可选择是否包含奖励目标）
    // 参数：bonus-是否包含奖励目标检查，返回值：场景是否完成
    bool IsCompleted(bool bonus) const;

    // 获取步骤数量：返回场景中的步骤总数（可选择是否包含奖励步骤）
    // 参数：withBonus-是否包含奖励步骤，返回值：步骤总数
    uint8 GetStepCount(bool withBonus) const;

    // 更新当前步骤：检查步骤完成状态并自动推进场景进度
    // 参数：loading-是否在加载过程中（影响通知发送）
    void UpdateCurrentStep(bool loading);

    // 设置当前步骤：手动设置场景的当前执行步骤
    // 参数：step-新的当前步骤索引
    void SetCurrentStep(uint8 step);

    // 发放奖励：处理场景完成后的奖励发放逻辑
    // 参数：bonus-是否为奖励目标奖励，rewardStep-奖励步骤索引
    void Reward(bool bonus, uint32 rewardStep);

    // 获取成就管理器：返回场景关联的成就管理器引用（可修改版本）
    AchievementMgr<Scenario>& GetAchievementMgr();

    // 获取成就管理器：返回场景关联的成就管理器引用（只读版本）
    AchievementMgr<Scenario> const& GetAchievementMgr() const;

    // 更新成就标准：触发成就系统的标准检查和进度更新
    // 参数：type-标准类型，miscValue1-3-附加参数，unit-相关单位，referencePlayer-参考玩家
    void UpdateAchievementCriteria(CriteriaTypes type, uint32 miscValue1 = 0, uint32 miscValue2 = 0, uint32 miscValue3 = 0, Unit* unit = nullptr, Player* referencePlayer = nullptr);

    // 获取奖励目标数据：构建发送给客户端的奖励目标信息列表
    // 返回值：包含奖励目标ID和完成状态的数据结构向量
    std::vector<WorldPackets::Scenario::BonusObjectiveData> GetBonusObjectivesData();

    // 发送步骤更新：向客户端发送场景状态和进度信息
    // 参数：player-目标玩家（nullptr表示广播），full-是否发送完整信息
    void SendStepUpdate(Player* player = nullptr, bool full = false);

    // 发送完成数据包：向指定玩家发送场景完成通知
    // 参数：player-目标玩家指针
    void SendFinishPacket(Player* player);

    // 发送标准更新：向所有玩家广播成就标准进度更新
    // 参数：progress-进度数据指针，timeElapsed-经过时间（毫秒）
    void SendCriteriaUpdate(CriteriaProgress const* progress, uint32 timeElapsed = 0);

    // 广播数据包：根据场景类型选择合适的广播机制发送数据包
    // 参数：data-要广播的数据包指针
    void BroadCastPacket(const WorldPacket* data);

    // 根据步骤获取标准树ID：查找指定步骤对应的成就标准树标识符
    // 参数：step-步骤索引，返回值：标准树ID
    uint32 GetScenarioCriteriaByStep(uint8 step);

    // 检查标准更新权限：递归检查是否允许更新指定的成就标准
    // 参数：criteriaTreeId-标准树ID，recursTree-递归树ID，返回值：是否可以更新
    bool CanUpdateCriteria(uint32 criteriaTreeId, uint32 recursTree = 0) const;

    // 获取挑战对象：返回关联的大秘境挑战模式对象指针
    Challenge* GetChallenge();

    // 更新锁：线程安全的共享互斥锁，用于保护场景数据的并发访问
    sf::contention_free_shared_mutex< > i_updateLock;

    // 设置户外PvP：配置场景的户外PvP管理对象和区域ID
    // 参数：outdoor-户外PvP管理器指针，zone-PvP区域ID
    void SetOutdoorPvP(OutdoorPvP* outdoor, uint32 zone);

protected:
    // 挑战模式对象指针：管理大秘境挑战的相关数据和逻辑
    Challenge* _challenge;

    // 场景条目指针：指向场景数据库条目，包含场景的基础配置信息
    ScenarioEntry const* _scenarioEntry;

    // 步骤状态映射表：存储每个场景步骤的当前执行状态
    std::map<ScenarioStepEntry const*, ScenarioStepState> _stepStates;

    // 实例ID：当前场景所在的地图实例唯一标识符
    uint32 instanceId;

    // 场景ID：当前场景的唯一标识符，对应Scenario.db2中的ID
    uint32 scenarioId;

    // LFG副本数据指针：指向寻找队伍系统的副本配置数据
    lfg::LFGDungeonData const* dungeonData;

    // 成就管理器：处理场景相关的成就和标准检查
    AchievementMgr<Scenario> m_achievementMgr;

    // 当前地图指针：场景关联的地图对象，用于地理位置和实例管理
    Map* curMap;

    // 当前步骤索引：场景当前执行的步骤编号（从0开始）
    uint8 currentStep;

    // 当前标准树ID：当前步骤对应的成就标准树标识符
    uint32 currentTree;

    // 场景步骤列表：包含场景所有步骤的有序集合
    ScenarioSteps steps;

    // 活跃步骤列表：当前正在进行的步骤ID列表，用于客户端显示
    std::vector<uint32> ActiveSteps;

    // 主要奖励已发放标志：标记场景主要目标的奖励是否已经发放
    bool rewarded;

    // 奖励目标奖励已发放标志：标记奖励目标的额外奖励是否已经发放
    bool bonusRewarded;

    // 包含奖励目标标志：标记场景是否包含可选的奖励目标
    bool hasbonus;

    // 户外PvP管理器指针：管理户外PvP场景的对象（默认为空）
    OutdoorPvP* m_outdoorPvp = nullptr;

    // 户外PvP区域ID：户外PvP场景的区域标识符（默认为0）
    uint32 m_outdoorPvpZone = 0;
};

#endif
