/*
 * Copyright (C) 2008-2014 TrinityCore <http://www.trinitycore.org/>
 *
 * This program is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License as published by the
 * Free Software Foundation; either version 2 of the License, or (at your
 * option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
 * more details.
 *
 * You should have received a copy of the GNU General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

#include "ScenarioMgr.h"
#include "LFGMgr.h"
#include "WorldSession.h"

// 场景管理器构造函数 - 初始化场景步骤数据结构，从DB2数据库加载所有场景步骤
// 功能作用：构建场景步骤的快速查找结构，支持普通场景和大秘境词缀场景的分别管理
// 设计原理：使用向量和映射表的组合结构，平衡查找性能和内存使用
// 技术优势：预分配内存空间避免动态扩容，分类存储支持不同场景类型的特殊需求
// 语义含义：建立场景系统的基础数据索引，为后续场景创建和管理提供数据支撑
// 底层机制：基于DB2Store的数据遍历，构建多层次的数据访问结构
ScenarioMgr::ScenarioMgr()
{
    // 初始化场景步骤快速访问向量 - 预分配足够的空间以容纳所有可能的场景ID
    // 使用场景数据库行数+1确保索引不会越界，所有元素初始化为nullptr
    m_stepVector.assign(sScenarioStore.GetNumRows() + 1, nullptr);

    // 遍历所有场景步骤条目 - 从ScenarioStep.db2数据库中加载步骤数据
    for (ScenarioStepEntry const* entry : sScenarioStepStore)
    {
        // 验证场景ID有效性 - 确保步骤对应的场景在Scenario.db2中存在
        if (!sScenarioStore.LookupEntry(entry->ScenarioID))
            continue; // 跳过无效的场景步骤

        // 检查是否为大秘境"繁盛"词缀步骤 - 繁盛词缀会增加额外的非首领敌人
        if (HasAffixesTeeming(entry->Criteriatreeid)) // Affixes::Teeming - 大秘境繁盛词缀检查
        {
            // 繁盛词缀步骤总是使用索引0 - 简化词缀步骤的管理逻辑
            uint8 orderIndex = 0/*entry->OrderIndex*/; // allways step 0 - 始终为步骤0

            // 确保繁盛步骤容器有足够空间 - 动态调整容器大小以容纳新步骤
            if (m_stepTeemingMap[entry->ScenarioID].size() <= orderIndex)
                m_stepTeemingMap[entry->ScenarioID].resize(orderIndex + 1);

            // 存储繁盛词缀步骤 - 将步骤数据存入专用的繁盛词缀映射表
            m_stepTeemingMap[entry->ScenarioID][orderIndex] = entry;
            continue; // 处理完繁盛步骤后继续下一个条目
        }

        // 处理普通场景步骤 - 确保步骤容器有足够空间容纳当前步骤索引
        if (m_stepMap[entry->ScenarioID].size() <= entry->OrderIndex)
            m_stepMap[entry->ScenarioID].resize(entry->OrderIndex + 1);

        // 存储普通场景步骤 - 按步骤顺序索引存储到对应场景的步骤列表中
        m_stepMap[entry->ScenarioID][entry->OrderIndex] = entry;

        // 建立快速访问指针 - 在向量中存储指向步骤列表的指针，实现O(1)查找
        m_stepVector[entry->ScenarioID] = &m_stepMap[entry->ScenarioID];
    }
}

// 场景管理器析构函数 - 清理所有场景资源，防止内存泄漏
// 功能作用：安全释放所有场景对象的内存，确保程序正常退出
// 设计原理：遍历删除模式，确保每个场景对象都被正确析构
// 技术优势：自动资源管理，避免手动释放导致的遗漏
// 语义含义：场景管理器生命周期结束时的清理工作
// 底层机制：STL容器的迭代器遍历和动态内存释放
ScenarioMgr::~ScenarioMgr()
{
    // 遍历所有存储的场景对象 - 逐个释放场景实例的内存
    for (auto v : _scenarioStore)
        delete v.second; // 删除场景对象指针，释放动态分配的内存
}

// 卸载所有场景数据 - 清理成就管理器缓存，用于服务器热重载
// 功能作用：在不重启服务器的情况下重新加载场景配置
// 设计原理：保留场景对象但清理缓存数据，支持动态配置更新
// 技术优势：热重载机制提升开发和运维效率
// 语义含义：场景数据的软重置，保持服务连续性
// 底层机制：成就管理器的映射表清理，释放缓存占用的内存
void ScenarioMgr::UnloadAll()
{
    // 遍历所有场景对象 - 清理每个场景的成就管理器缓存
    for (auto v : _scenarioStore)
        v.second->GetAchievementMgr().ClearMap(); // 清空成就管理器的映射表缓存
}

// 获取场景管理器单例实例 - 实现全局唯一的场景管理器访问
// 功能作用：提供线程安全的单例访问，确保全局只有一个场景管理器实例
// 设计原理：静态局部变量实现的懒汉式单例模式
// 技术优势：C++11保证的线程安全初始化，避免双重检查锁定
// 语义含义：场景系统的全局访问入口
// 底层机制：静态存储期变量的自动初始化和生命周期管理
ScenarioMgr* ScenarioMgr::instance()
{
    static ScenarioMgr instance; // 静态局部变量确保单例特性和线程安全
    return &instance; // 返回单例实例的地址
}

// 移除场景 - 从管理器中删除指定实例的场景对象
// 功能作用：安全移除场景实例，释放相关资源
// 设计原理：查找-验证-删除的安全操作模式
// 技术优势：异常安全的删除操作，避免访问无效指针
// 语义含义：场景生命周期的正常结束处理
// 底层机制：STL映射表的查找和删除操作
void ScenarioMgr::RemoveScenario(uint32 instanceId)
{
    // 在场景存储容器中查找指定实例ID的场景
    ScenarioMap::iterator itr = _scenarioStore.find(instanceId);

    // 检查场景是否存在 - 避免删除不存在的场景导致的错误
    if (itr == _scenarioStore.end())
        return; // 场景不存在，直接返回

    // 安全删除场景对象 - 先释放内存再从容器中移除
    delete itr->second; // 删除场景对象，释放动态分配的内存
    _scenarioStore.erase(itr); // 从存储容器中移除场景条目
}

// 获取场景步骤列表 - 根据场景ID和词缀类型返回对应的步骤数据
// 功能作用：为场景实例提供步骤数据，支持普通模式和大秘境词缀模式
// 设计原理：优先级查找策略，词缀步骤优先于普通步骤
// 技术优势：O(1)时间复杂度的快速查找，支持不同场景类型的差异化处理
// 语义含义：场景步骤数据的统一访问接口
// 底层机制：基于向量索引和映射表查找的混合访问模式
ScenarioSteps const* ScenarioMgr::GetScenarioSteps(uint32 scenarioId, bool Teeming)
{
    // 检查是否请求大秘境"繁盛"词缀步骤
    if (Teeming)
        // 尝试从繁盛词缀步骤映射表中获取步骤数据
        if (ScenarioSteps const* steps = Trinity::Containers::MapGetValuePtr(m_stepTeemingMap, scenarioId))
            return steps; // 返回繁盛词缀专用的步骤列表

    // 返回普通场景步骤 - 使用快速访问向量获取步骤数据
    return m_stepVector[scenarioId]; // 通过场景ID直接索引获取步骤列表指针
}

// 添加场景到管理器 - 为指定地图创建新的场景实例并注册到管理系统
// 功能作用：创建场景对象，建立地图与场景的关联关系
// 设计原理：工厂模式创建场景，双向关联确保数据一致性
// 技术优势：统一的场景创建入口，自动处理实例ID冲突
// 语义含义：场景生命周期的开始阶段，建立场景运行环境
// 底层机制：动态内存分配和容器插入操作
Scenario* ScenarioMgr::AddScenario(Map* map, lfg::LFGDungeonData const* dungeonData, Player* player, bool find)
{
    // 检查实例ID冲突 - 确保同一实例不会创建多个场景
    if (_scenarioStore.find(map->GetInstanceId()) != _scenarioStore.end())
        return nullptr; // 场景已存在，返回空指针避免重复创建

    // 创建新的场景对象 - 使用工厂模式构造场景实例
    Scenario* scenario = new Scenario(map, dungeonData, player, find);

    // 注册场景到管理器 - 建立实例ID到场景对象的映射关系
    _scenarioStore[map->GetInstanceId()] = scenario;

    // 建立地图与场景的双向关联 - 确保地图对象能够访问其场景
    map->m_scenarios.insert(scenario);

    // 返回创建的场景对象 - 供调用者进行后续操作
    return scenario;
}

// 获取场景对象 - 根据实例ID查找对应的场景实例
// 功能作用：提供场景对象的快速查找服务
// 设计原理：封装底层容器访问，提供类型安全的查找接口
// 技术优势：使用Trinity框架的安全容器访问函数，避免越界访问
// 语义含义：场景对象的统一访问入口
// 底层机制：STL映射表的查找操作，返回值指针或nullptr
Scenario* ScenarioMgr::GetScenario(uint32 instanceId)
{
    // 使用Trinity框架的安全映射访问函数 - 返回场景指针或nullptr
    return Trinity::Containers::MapGetValuePtr(_scenarioStore, instanceId);
}

// 检查场景步骤存在性 - 判断指定副本和玩家组合是否有对应的场景步骤
// 功能作用：在创建场景前验证场景数据的完整性，避免创建无效场景
// 设计原理：多级查找策略，优先级从高到低检查不同的场景ID来源
// 技术优势：全面的场景ID获取逻辑，支持多种场景创建场景
// 语义含义：场景系统的前置验证机制
// 底层机制：条件分支查找和数据库查询的组合
bool ScenarioMgr::HasScenarioStep(lfg::LFGDungeonData const* _dungeonData, Player* player)
{
    uint32 scenarioId = 0; // 初始化场景ID变量

    // 第一优先级：检查玩家当前的场景ID - 玩家已经在某个场景中
    if (player->GetScenarioId())
        scenarioId = player->GetScenarioId();
    // 第二优先级：根据地图、难度、阵营、职业查找场景数据 - 从自定义场景数据表查找
    else if (ScenarioData const* scenarioData = sObjectMgr->GetScenarioOnMap(_dungeonData->map, _dungeonData->difficulty, player->GetTeam(), player->getClass(), _dungeonData->id))
        scenarioId = scenarioData->ScenarioID;
    // 第三优先级：使用LFG副本数据中的场景ID - 从DBC数据获取默认场景
    else if (_dungeonData->dbc->ScenarioID)
        scenarioId = _dungeonData->dbc->ScenarioID;

    // 验证场景ID有效性 - 确保找到了有效的场景ID
    if (!scenarioId)
        return false; // 没有找到场景ID，返回false

    // 检查场景步骤数据存在性 - 验证场景ID对应的步骤数据是否已加载
    return m_stepVector[scenarioId] != nullptr;
}

// 检查大秘境"繁盛"词缀 - 判断指定标准树ID是否属于繁盛词缀相关的挑战
// 功能作用：识别大秘境中的繁盛词缀，为词缀系统提供特殊处理逻辑
// 设计原理：硬编码的ID列表匹配，确保词缀识别的准确性
// 技术优势：快速的switch-case查找，O(1)时间复杂度
// 语义含义：大秘境词缀系统的核心识别机制
// 底层机制：编译时优化的跳转表，高效的条件分支处理
bool ScenarioMgr::HasAffixesTeeming(uint16 CriteriaTreeID)
{
    // 使用switch语句匹配繁盛词缀的标准树ID列表
    // 这些ID对应7.3.5版本中启用繁盛词缀的大秘境副本
    switch (CriteriaTreeID)
    {
        case 47415: // 7.0 Dungeon - Valhallas - Challenge (More Trash)
        case 50599: // 繁盛词缀标准树ID - 7.0 地下城 - 黑鸦堡垒 - 挑战（更多小怪）
        case 51246: // 繁盛词缀标准树ID - 7.0 地下城 - 艾萨拉之眼 - 挑战（更多小怪）
        case 51710: // 繁盛词缀标准树ID - 7.0 地下城 - 黑心林地 - 挑战（更多小怪）
        case 51880: // 繁盛词缀标准树ID - 7.0 地下城 - 守望者地窟 - 挑战（更多小怪）
        case 52278: // 繁盛词缀标准树ID - 7.0 地下城 - 奈萨里奥的巢穴 - 挑战（更多小怪）
        case 52327: // 繁盛词缀标准树ID - 7.0 地下城 - 噬魂之喉 - 挑战（更多小怪）
        case 52427: // 繁盛词缀标准树ID - 7.0 地下城 - 魔法回廊 - 挑战（更多小怪）
        case 52471: // 繁盛词缀标准树ID - 7.0 地下城 - 群星庭院 - 挑战（更多小怪）
        case 57810: // 繁盛词缀标准树ID - 7.0 Return to Karazhan - Upper Return to Karazhan - Challenge (More Trash)
        case 57866: // 繁盛词缀标准树ID - 7.0 Return to Karazhan - Lower Return to Karazhan Challenge (More Trash)
        case 58715: // 繁盛词缀标准树ID - 7.0 Dungeon - Cathedral of Eternal Night - Challenge (More Trash)
        case 60933: // 繁盛词缀标准树ID - 7.0 Dungeon - Seat of the Triumvirate - Challenge (More Trash)
            return true; // 匹配到繁盛词缀ID，返回true
    }

    // 未匹配到任何繁盛词缀ID，返回false
    return false;
}
