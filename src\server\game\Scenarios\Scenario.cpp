/*
 * Copyright (C) 2008-2014 TrinityCore <http://www.trinitycore.org/>
 *
 * This program is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License as published by the
 * Free Software Foundation; either version 2 of the License, or (at your
 * option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
 * more details.
 *
 * You should have received a copy of the GNU General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

#include "Group.h"
#include "ScenarioMgr.h"
#include "LFGMgr.h"
#include "InstanceSaveMgr.h"
#include "WorldSession.h"
#include "ScenarioPackets.h"
#include "InstanceScript.h"
#include "QuestData.h"
#include "OutdoorPvP.h"

// 构造函数1：基于LFG副本数据创建场景对象 - Constructor 1: Create scenario object based on LFG dungeon data
// 参数说明：map-地图指针，_dungeonData-LFG副本数据，player-玩家指针，find-是否查找现有场景
Scenario::Scenario(Map* map, lfg::LFGDungeonData const* _dungeonData, Player* player, bool find) : m_achievementMgr(this) // 初始化成就管理器，传入this指针作为场景对象引用 - Initialize achievement manager with this pointer as scenario reference
{
    if (!map) // 地图指针验证，防止空指针访问 - Map pointer validation to prevent null pointer access
        return;

    curMap = map; // 设置当前地图指针 - Set current map pointer
    instanceId = map->GetInstanceId(); // 获取地图实例ID，用于唯一标识副本 - Get map instance ID for unique instance identification
    dungeonData = _dungeonData; // 保存LFG副本数据引用 - Save LFG dungeon data reference
    currentStep = 0; // 初始化当前步骤为0（第一步） - Initialize current step to 0 (first step)
    currentTree = 0; // 初始化当前标准树ID为0 - Initialize current criteria tree ID to 0
    bonusRewarded = false; // 初始化奖励目标奖励状态为未获得 - Initialize bonus objective reward status as not received
    rewarded = false; // 初始化主要奖励状态为未获得 - Initialize main reward status as not received
    scenarioId = dungeonData->dbc->ScenarioID; // 从LFG副本数据中获取场景ID - Get scenario ID from LFG dungeon data
    _challenge = nullptr; // 初始化挑战模式指针为空 - Initialize challenge mode pointer to null
    hasbonus = false; // 初始化是否有奖励目标标志为false - Initialize has bonus objective flag to false

    if (player->GetScenarioId()) // 检查玩家是否已有场景ID（可能来自之前的场景） - Check if player already has scenario ID (possibly from previous scenario)
    {
        scenarioId = player->GetScenarioId(); // 使用玩家的场景ID覆盖默认值 - Use player's scenario ID to override default value
        find = true; // 设置查找标志为true，表示使用现有场景 - Set find flag to true, indicating use of existing scenario
        player->SetScenarioId(0); // 清除玩家的场景ID，避免重复使用 - Clear player's scenario ID to avoid reuse
    }

    if (!find) // 如果不是查找现有场景，则根据地图和玩家信息查找合适的场景 - If not finding existing scenario, look for appropriate scenario based on map and player info
        if (ScenarioData const* scenarioData = sObjectMgr->GetScenarioOnMap(dungeonData->map, map->GetDifficultyID(), player->GetTeam(), player->getClass(), dungeonData->id)) // 根据地图ID、难度、阵营、职业、副本ID查找场景数据 - Find scenario data based on map ID, difficulty, faction, class, dungeon ID
            scenarioId = scenarioData->ScenarioID; // 使用查找到的场景ID - Use found scenario ID

    _scenarioEntry = sScenarioStore.LookupEntry(scenarioId); // 从场景数据存储中查找场景条目 - Look up scenario entry from scenario data store
    if (!_scenarioEntry) // 场景条目验证，确保场景数据有效 - Scenario entry validation to ensure scenario data is valid
    {
        //ASSERT(_scenarioEntry); // 断言检查（已注释） - Assert check (commented out)
        // TC_LOG_DEBUG(LOG_FILTER_CHALLENGE, "Scenario::Scenario instanceId %u scenarioId %u dungeonData %u", instanceId, scenarioId, dungeonData->map); // 调试日志（已注释） - Debug log (commented out)
        return; // 场景条目无效时直接返回，避免后续错误 - Return directly when scenario entry is invalid to avoid subsequent errors
    }

    // TC_LOG_DEBUG(LOG_FILTER_CHALLENGE, "Scenario::Scenario instanceId %u scenarioId %u dungeonData %u", instanceId, scenarioId, dungeonData->map); // 调试日志：记录实例ID、场景ID、地图ID - Debug log: record instance ID, scenario ID, map ID

    ScenarioSteps const* _steps = sScenarioMgr->GetScenarioSteps(scenarioId); // 从场景管理器获取场景步骤列表 - Get scenario steps list from scenario manager
    ASSERT(_steps); // 断言确保步骤列表有效 - Assert to ensure steps list is valid

    steps = *_steps; // 复制步骤列表到成员变量 - Copy steps list to member variable
    currentTree = GetScenarioCriteriaByStep(currentStep); // 获取当前步骤对应的标准树ID - Get criteria tree ID corresponding to current step
    ActiveSteps.push_back(steps[currentStep]->ID); // 将当前步骤ID添加到活跃步骤列表 - Add current step ID to active steps list

    for (auto const& step : steps) // 遍历所有场景步骤进行初始化 - Iterate through all scenario steps for initialization
    {
        SetStepState(step, SCENARIO_STEP_NOT_STARTED); // 设置步骤状态为未开始 - Set step state to not started
        if (step->IsBonusObjective()) // 检查是否为奖励目标步骤 - Check if it's a bonus objective step
            hasbonus = true; // 标记场景包含奖励目标 - Mark scenario contains bonus objectives
    }

    if (curMap->isChallenge()) // 检查当前地图是否为挑战模式（大秘境） - Check if current map is challenge mode (mythic keystone)
        CreateChallenge(player); // 创建挑战模式对象 - Create challenge mode object

    objectCountInWorld[uint8(HighGuid::Scenario)]++; // 增加全局场景对象计数器，用于统计和调试 - Increment global scenario object counter for statistics and debugging
}

// 构造函数2：基于场景ID创建场景对象（简化版本） - Constructor 2: Create scenario object based on scenario ID (simplified version)
// 参数说明：map-地图指针，_scenarioId-场景ID
Scenario::Scenario(Map* map, uint32 _scenarioId) : m_achievementMgr(this) // 初始化成就管理器，传入this指针作为场景对象引用 - Initialize achievement manager with this pointer as scenario reference
{
    if (!map) // 地图指针验证，防止空指针访问 - Map pointer validation to prevent null pointer access
        return;

    curMap = map; // 设置当前地图指针 - Set current map pointer
    instanceId = map->GetInstanceId(); // 获取地图实例ID，用于唯一标识副本 - Get map instance ID for unique instance identification
    dungeonData = nullptr; // 设置LFG副本数据为空（此构造函数不依赖LFG数据） - Set LFG dungeon data to null (this constructor doesn't depend on LFG data)
    currentStep = 0; // 初始化当前步骤为0（第一步） - Initialize current step to 0 (first step)
    currentTree = 0; // 初始化当前标准树ID为0 - Initialize current criteria tree ID to 0
    bonusRewarded = false; // 初始化奖励目标奖励状态为未获得 - Initialize bonus objective reward status as not received
    rewarded = false; // 初始化主要奖励状态为未获得 - Initialize main reward status as not received
    scenarioId = _scenarioId; // 直接使用传入的场景ID - Directly use the passed scenario ID
    _challenge = nullptr; // 初始化挑战模式指针为空 - Initialize challenge mode pointer to null
    hasbonus = false; // 初始化是否有奖励目标标志为false - Initialize has bonus objective flag to false

    _scenarioEntry = sScenarioStore.LookupEntry(scenarioId); // 从场景数据存储中查找场景条目 - Look up scenario entry from scenario data store
    if (!_scenarioEntry) // 场景条目验证，确保场景数据有效 - Scenario entry validation to ensure scenario data is valid
    {
        //ASSERT(_scenarioEntry); // 断言检查（已注释） - Assert check (commented out)
        // TC_LOG_DEBUG(LOG_FILTER_CHALLENGE, "Scenario::Scenario instanceId %u scenarioId %u dungeonData %u", instanceId, scenarioId, dungeonData->map); // 调试日志（已注释，注意dungeonData为空） - Debug log (commented out, note dungeonData is null)
        return; // 场景条目无效时直接返回，避免后续错误 - Return directly when scenario entry is invalid to avoid subsequent errors
    }

    // TC_LOG_DEBUG(LOG_FILTER_CHALLENGE, "Scenario::Scenario instanceId %u scenarioId %u dungeonData %u", instanceId, scenarioId, dungeonData->map); // 调试日志：记录实例ID、场景ID（dungeonData为空） - Debug log: record instance ID, scenario ID (dungeonData is null)

    ScenarioSteps const* _steps = sScenarioMgr->GetScenarioSteps(scenarioId); // 从场景管理器获取场景步骤列表 - Get scenario steps list from scenario manager
    ASSERT(_steps); // 断言确保步骤列表有效 - Assert to ensure steps list is valid

    steps = *_steps; // 复制步骤列表到成员变量 - Copy steps list to member variable
    currentTree = GetScenarioCriteriaByStep(currentStep); // 获取当前步骤对应的标准树ID - Get criteria tree ID corresponding to current step
    ActiveSteps.push_back(steps[currentStep]->ID); // 将当前步骤ID添加到活跃步骤列表 - Add current step ID to active steps list

    for (auto const& step : steps) // 遍历所有场景步骤进行初始化 - Iterate through all scenario steps for initialization
    {
        SetStepState(step, SCENARIO_STEP_NOT_STARTED); // 设置步骤状态为未开始 - Set step state to not started
        if (step->IsBonusObjective()) // 检查是否为奖励目标步骤 - Check if it's a bonus objective step
            hasbonus = true; // 标记场景包含奖励目标 - Mark scenario contains bonus objectives
    }

    objectCountInWorld[uint8(HighGuid::Scenario)]++; // 增加全局场景对象计数器，用于统计和调试 - Increment global scenario object counter for statistics and debugging
}

// 析构函数：清理场景对象资源 - Destructor: Clean up scenario object resources
Scenario::~Scenario()
{
    delete _challenge; // 删除挑战模式对象，释放内存资源 - Delete challenge mode object to free memory resources
    _challenge = nullptr; // 将指针设为空，防止悬空指针访问 - Set pointer to null to prevent dangling pointer access

    objectCountInWorld[uint8(HighGuid::Scenario)]--; // 减少全局场景对象计数器，维护准确的对象统计 - Decrement global scenario object counter to maintain accurate object statistics
}

void Scenario::Update(const uint32 t_diff)
{
    m_achievementMgr.UpdateTimedAchievements(t_diff);
}

// 创建挑战模式（大秘境）函数 - 负责初始化大秘境挑战系统
void Scenario::CreateChallenge(Player* player)
{
    // TC_LOG_DEBUG(LOG_FILTER_CHALLENGE, "CreateChallenge player %u map %u", bool(player), bool(GetMap())); // 调试日志：记录玩家和地图状态

    Map* map = GetMap(); // 获取当前场景关联的地图对象
    if (!player || !map) // 验证玩家指针和地图指针的有效性，防止空指针访问
        return;

    // 获取挑战模式条目：优先从队伍获取，如果玩家没有队伍则从个人钥石信息获取
    MapChallengeModeEntry const* m_challengeEntry = player->GetGroup() ? player->GetGroup()->m_challengeEntry : player->m_challengeKeyInfo.challengeEntry;
    if (!m_challengeEntry) // 验证挑战模式条目是否存在，没有则无法创建挑战
        return;

    // 创建挑战模式对象，传入地图、玩家、实例ID和场景对象引用
    _challenge = new Challenge(map, player, GetInstanceId(), this);

    // TC_LOG_DEBUG(LOG_FILTER_CHALLENGE, "CreateChallenge _challenge %u _canRun %u", bool(_challenge), bool(_challenge->_canRun)); // 调试日志：记录挑战对象创建状态

    if (!_challenge || !_challenge->_canRun) // 验证挑战对象创建成功且可以运行
        return;

    // 建立挑战模式与实例脚本的双向关联
    if (Map* map_ = GetMap()) // 重新获取地图对象（防御性编程）
    {
        if (InstanceMap* instanceMap = map_->ToInstanceMap()) // 将地图转换为实例地图
        {
            if (InstanceScript* script = instanceMap->GetInstanceScript()) // 获取实例脚本对象
            {
                script->SetChallenge(_challenge); // 在实例脚本中设置挑战模式引用
                _challenge->SetInstanceScript(script); // 在挑战模式中设置实例脚本引用
            }
        }
    }

    // 尝试从对象管理器获取大秘境难度的场景数据
    if (ScenarioData const* scenarioData = sObjectMgr->GetScenarioOnMap(map->GetId(), DIFFICULTY_MYTHIC_KEYSTONE))
        scenarioId = scenarioData->ScenarioID; // 使用找到的场景ID

    // 根据挑战模式条目ID映射到特定的场景ID（硬编码映射表）
    switch (m_challengeEntry->ID)
    {
        case 197: // Eye of Azshara - 艾萨拉之眼
            scenarioId = 1169;
            break;
        case 198: // Darkheart Thicket - 黑心林地
            scenarioId = 1172;
            break;
        case 199: // Black Rook Hold - 黑鸦堡垒
            scenarioId = 1166;
            break;
        case 200: // Halls of Valor - 英灵殿
            scenarioId = 1046;
            break;
        case 206: // Neltharion's Lair - 奈萨里奥的巢穴
            scenarioId = 1174;
            break;
        case 207: // Vault of the Wardens - 守望者地窟
            scenarioId = 1173;
            break;
        case 208: // Maw of Souls - 噬魂之喉
            scenarioId = 1175;
            break;
        case 209: // The Arcway - 魔法回廊
            scenarioId = 1177;
            break;
        case 210: // Court of Stars - 群星庭院
            scenarioId = 1178;
            break;
        case 227: // Return to Karazhan: Lower - 重返卡拉赞：下层
            scenarioId = 1309;
            break;
        case 233: // Cathedral of Eternal Night - 永夜大教堂
            scenarioId = 1335;
            break;
        case 234: // Return to Karazhan: Upper - 重返卡拉赞：上层
            scenarioId = 1308;
            break;
        default: // 默认情况：不修改场景ID
            break;
    }

    // 从场景数据存储中查找更新后的场景条目
    _scenarioEntry = sScenarioStore.LookupEntry(scenarioId);
    ASSERT(_scenarioEntry); // 断言确保场景条目有效

    // 获取场景步骤，考虑"繁盛"词缀的影响（繁盛词缀会增加额外的非首领敌人）
    ScenarioSteps const* _steps = sScenarioMgr->GetScenarioSteps(scenarioId, _challenge->HasAffix(Affixes::Teeming));
    ASSERT(_steps); // 断言确保步骤列表有效

    currentStep = 0; // 重置当前步骤为第一步
    steps = *_steps; // 更新场景步骤列表
    if (steps.size() <= currentStep) // 验证步骤列表不为空且当前步骤索引有效
    {
        // TC_LOG_DEBUG(LOG_FILTER_CHALLENGE, "CreateChallenge steps %u currentStep %u", steps.size(), currentStep); // 调试日志：记录步骤数量异常
        return;
    }

    currentTree = GetScenarioCriteriaByStep(currentStep); // 获取当前步骤对应的标准树ID
    ActiveSteps.clear(); // 清空活跃步骤列表
    ActiveSteps.push_back(steps[currentStep]->ID); // 添加当前步骤ID到活跃步骤列表

    // 重新初始化所有步骤状态为未开始
    for (auto const& step : steps)
        SetStepState(step, SCENARIO_STEP_NOT_STARTED);

    SetCurrentStep(0); // 设置当前步骤为第一步，触发相关的状态更新和通知

    // TC_LOG_ERROR(LOG_FILTER_CHALLENGE, "%s %u, mapID: %u, scenarioID: %u", __FUNCTION__, __LINE__, map->GetId(), scenarioId); // 调试日志：记录函数执行完成状态
}

uint32 Scenario::GetInstanceId() const
{
    return instanceId;
}

Map* Scenario::GetMap()
{
    return curMap;
}

OutdoorPvP* Scenario::GetOutdoorPvP()
{
    return m_outdoorPvp;
}

uint32 Scenario::GetOutdoorPvPZone() const
{
    return m_outdoorPvpZone;
}


uint32 Scenario::GetScenarioId() const
{
    return scenarioId;
}

uint32 Scenario::GetCurrentStep() const
{
    return currentStep;
}

void Scenario::SetStepState(ScenarioStepEntry const* step, ScenarioStepState state)
{
    _stepStates[step] = state;
}

bool Scenario::IsCompleted(bool bonus) const
{
    return currentStep == GetStepCount(bonus);
}

// 获取场景步骤数量函数 - 根据是否包含奖励目标返回不同的步骤计数
uint8 Scenario::GetStepCount(bool withBonus) const
{
    if (withBonus) // 如果包含奖励目标，返回所有步骤的总数（包括主要步骤和奖励步骤）
        return steps.size();

    uint8 count = 0; // 初始化主要步骤计数器
    for (auto const& v : steps) // 遍历所有场景步骤
        if (!v->IsBonusObjective()) // 检查步骤是否不是奖励目标（即主要步骤）
            ++count; // 增加主要步骤计数

    return count; // 返回主要步骤的数量（不包括奖励目标）
}

// 设置当前步骤函数 - 更新场景当前步骤并同步到相关系统
void Scenario::SetCurrentStep(uint8 step)
{
    currentStep = step; // 设置新的当前步骤索引
    currentTree = GetScenarioCriteriaByStep(currentStep); // 获取新步骤对应的标准树ID

    SendStepUpdate(); // 向客户端发送步骤更新数据包，更新UI显示

    // 处理户外PvP场景的步骤同步
    if (OutdoorPvP* outDoorPvP = GetOutdoorPvP()) // 检查是否为户外PvP场景
        outDoorPvP->SetData(GetOutdoorPvPZone(), currentStep); // 在户外PvP系统中设置当前步骤数据
    else if (Map *m = GetMap()) // 如果不是户外PvP，处理普通副本场景
    {
        if (InstanceMap* i = m->ToInstanceMap()) // 将地图转换为实例地图
        {
            if (InstanceScript *script = i->GetInstanceScript()) // 获取实例脚本对象
            {
                script->setScenarioStep(currentStep); // 在实例脚本中设置场景步骤
                script->onScenarionNextStep(currentStep); // 触发实例脚本的场景步骤切换事件
                script->UpdatePhasing(); // 更新实例的相位系统（控制玩家看到的内容）
            }
            else
                i->UpdatePhasing(); // 如果没有实例脚本，直接在实例地图上更新相位
        }
    }

    ActiveSteps.push_back(steps[currentStep]->ID); // 将新的当前步骤ID添加到活跃步骤列表
}

// 更新当前步骤函数 - 检查步骤完成状态并自动推进场景进度
void Scenario::UpdateCurrentStep(bool loading)
{
    // i_updateLock.lock(); // 线程锁（已注释）- 防止并发访问时的数据竞争
    uint8 oldStep = currentStep; // 保存旧的步骤索引，用于后续比较和状态更新

    // 遍历所有场景步骤，检查是否有已完成的步骤需要推进进度
    for (ScenarioSteps::const_iterator itr = steps.begin(); itr != steps.end(); ++itr)
    {
        //Not check if ctep already complete // 不检查已经完成的步骤
        if (currentStep > (*itr)->OrderIndex) // 如果当前步骤已经超过了这个步骤的顺序索引，跳过检查
            continue;

        // 从标准树存储中查找步骤对应的标准树条目
        CriteriaTreeEntry const* criteriaTree = sCriteriaTreeStore.LookupEntry((*itr)->Criteriatreeid);
        if (!criteriaTree) // 如果标准树条目不存在，跳过这个步骤
            continue;

        // 检查成就管理器中该标准树是否已完成
        if (GetAchievementMgr().IsCompletedScenarioTree(criteriaTree))
        {
            currentStep = (*itr)->OrderIndex + 1; // 将当前步骤设置为已完成步骤的下一步
            currentTree = GetScenarioCriteriaByStep(currentStep); // 更新当前标准树ID
        }
    }

    // TC_LOG_DEBUG(LOG_FILTER_CHALLENGE, "UpdateCurrentStep currentStep %u oldStep %u loading %u steps %u", currentStep, oldStep, loading, steps.size()); // 调试日志：记录步骤更新状态

    // 如果步骤发生了变化且不是在加载过程中，执行步骤切换逻辑
    if (currentStep != oldStep && !loading)
    {
        // 验证新步骤的有效性（不为0且在有效范围内）
        if (currentStep != 0 && currentStep < steps.size())
        {
            // 处理户外PvP场景的步骤同步
            if (OutdoorPvP* outDoorPvP = GetOutdoorPvP())
                outDoorPvP->SetData(GetOutdoorPvPZone(), currentStep); // 在户外PvP系统中更新步骤数据
            else if (Map *m = GetMap()) // 处理普通副本场景
            {
                if (InstanceMap* i = m->ToInstanceMap()) // 转换为实例地图
                {
                    if (InstanceScript *script = i->GetInstanceScript()) // 获取实例脚本
                    {
                        script->setScenarioStep(currentStep); // 在实例脚本中设置新的场景步骤
                        script->onScenarionNextStep(currentStep); // 触发场景步骤切换事件处理
                        script->UpdatePhasing(); // 更新相位系统，可能改变玩家看到的NPC、物体等
                    }
                    else
                        i->UpdatePhasing(); // 如果没有实例脚本，直接更新实例地图的相位
                }
            }
            ActiveSteps.push_back(steps[currentStep]->ID); // 将新步骤ID添加到活跃步骤列表
        }

        SendStepUpdate(); // 向客户端发送步骤更新通知

        // 检查场景是否完成并处理奖励
        if (IsCompleted(false)) // 检查主要目标是否全部完成
            Reward(false, oldStep); // 给予主要完成奖励
        else if (hasbonus && IsCompleted(true)) // 如果有奖励目标且包括奖励目标在内全部完成
            Reward(true, oldStep); // 给予奖励目标完成奖励
    }

    // 更新步骤状态
    if (currentStep < steps.size()) // 如果当前步骤索引有效
        SetStepState(steps[currentStep], SCENARIO_STEP_IN_PROGRESS); // 设置当前步骤状态为进行中

    SetStepState(steps[oldStep], SCENARIO_STEP_DONE); // 设置旧步骤状态为已完成
    // i_updateLock.unlock(); // 解锁线程锁（已注释）
    //TC_LOG_DEBUG(LOG_FILTER_CHALLENGE, "UpdateCurrentStep currentStep %u oldStep %u loading %u", currentStep, oldStep, loading); // 调试日志：记录函数执行完成状态
}

// 根据步骤索引获取场景标准树ID函数 - 用于将步骤与成就系统关联
uint32 Scenario::GetScenarioCriteriaByStep(uint8 step)
{
    if (steps.empty()) // 检查步骤列表是否为空，防止访问空容器
        return 0; // 返回0表示无效的标准树ID

    // 遍历所有场景步骤，查找匹配指定步骤索引的步骤
    for (auto const& step_ : steps)
        if (step_ && step == step_->OrderIndex) // 验证步骤指针有效且步骤索引匹配
            return step_->Criteriatreeid; // 返回该步骤对应的标准树ID，用于成就系统检查

    return 0; // 未找到匹配的步骤，返回无效标准树ID
}

// 场景奖励发放函数 - 处理场景完成后的各种奖励发放
void Scenario::Reward(bool bonus, uint32 rewardStep)
{
    // TC_LOG_DEBUG(LOG_FILTER_CHALLENGE, "Scenario::Reward bonus %u rewarded %u bonusRewarded %u rewardStep %u", bonus, rewarded, bonusRewarded, rewardStep); // 调试日志：记录奖励发放参数

    // 防止重复发放奖励的检查机制
    if (bonus && bonusRewarded) // 如果是奖励目标且已经发放过奖励目标奖励
        return;

    if (!bonus && rewarded) // 如果是主要目标且已经发放过主要奖励
        return;

    // 标记奖励发放状态，防止重复发放
    if (bonus)
        bonusRewarded = true; // 标记奖励目标奖励已发放
    else
        rewarded = true; // 标记主要奖励已发放

    ObjectGuid groupGuid; // 队伍GUID，用于LFG系统奖励处理
    Map* map = GetMap(); // 获取当前地图对象
    OutdoorPvP* pvpMap = GetOutdoorPvP(); // 获取户外PvP对象
    // map not created? bye-bye reward // 地图未创建则无法发放奖励
    if (!pvpMap && !map) // 验证至少有一个有效的地图环境
        return;

    // 从奖励步骤中获取关联的任务模板，用于任务奖励发放
    Quest const* quest = sQuestDataStore->GetQuestTemplate(steps[rewardStep]->RewardQuestID);

    // 处理户外PvP场景的奖励发放
    if (pvpMap)
    {
        // 对指定区域内的所有玩家应用奖励逻辑
        pvpMap->ApplyOnEveryPlayerInZone([quest, this](Player* player)
        {
            if (player->CanContact()) // 检查玩家是否可以接收奖励（在线且状态正常）
            {

                if (quest) // 如果存在关联任务，发放任务奖励
                    player->AddDelayedEvent(100, [player, quest]() -> void { if (player) player->RewardQuest(quest, 0, nullptr, false); }); // 延迟100ms发放任务奖励，避免时序问题

                // 更新玩家的场景完成成就进度
                player->UpdateAchievementCriteria(CRITERIA_TYPE_COMPLETE_SCENARIO, scenarioId, 1); // 更新特定场景完成成就
                player->UpdateAchievementCriteria(CRITERIA_TYPE_COMPLETE_SCENARIO_COUNT, 1); // 更新场景完成总数成就

                // 向客户端发送场景完成通知数据包
                WorldPackets::Scenario::ScenarioCompleted data;
                data.ScenarioID = scenarioId; // 设置完成的场景ID
                player->SendDirectMessage(data.Write()); // 发送场景完成通知到客户端
            }
        }, GetOutdoorPvPZone()); // 指定户外PvP区域ID

        pvpMap->SetData(GetOutdoorPvPZone(), 100); // 在户外PvP系统中设置完成标记（100表示完成状态）
    }
    else if (map) // 处理普通副本场景的奖励发放
    {
        // 对地图上的所有玩家应用奖励逻辑
        map->ApplyOnEveryPlayer([&groupGuid, this, quest](Player* player)
        {
            if (player->CanContact()) // 检查玩家是否可以接收奖励
            {
                // 获取队伍GUID，用于后续的LFG奖励处理
                if (groupGuid.IsEmpty())
                    groupGuid = player->GetGroup() ? player->GetGroup()->GetGUID() : ObjectGuid::Empty;

                // 如果是挑战模式，更新挑战模式完成成就
                if (_challenge)
                    player->UpdateAchievementCriteria(CRITERIA_TYPE_COMPLETE_CHALLENGE, _challenge->GetChallengeLevel()); // 传入挑战等级作为成就参数

                if (quest) // 发放任务奖励
                    player->AddDelayedEvent(100, [player, quest]() -> void { if (player) player->RewardQuest(quest, 0, nullptr, false); }); // 延迟发放任务奖励

                // 更新场景完成相关成就
                player->UpdateAchievementCriteria(CRITERIA_TYPE_COMPLETE_SCENARIO, scenarioId, 1); // 特定场景完成成就
                player->UpdateAchievementCriteria(CRITERIA_TYPE_COMPLETE_SCENARIO_COUNT, 1); // 场景完成总数成就

                // 发送场景完成通知
                WorldPackets::Scenario::ScenarioCompleted data;
                data.ScenarioID = scenarioId;
                player->SendDirectMessage(data.Write());
            }
        });
    }

    // should not happen    没组队不能单刷? // 理论上不应该发生的情况注释
    //if (groupGuid.IsEmpty()) // 检查队伍GUID是否为空（已注释的代码）
    //    return;

    // TC_LOG_DEBUG(LOG_FILTER_CHALLENGE, "Reward Type %u", _scenarioEntry->Type); // 调试日志：记录场景类型

    // 根据场景类型执行不同的奖励后处理逻辑
    switch (_scenarioEntry->Type)
    {
        case SCENARIO_TYPE_DEFAULT: // 默认场景类型
        case SCENARIO_TYPE_USE_DUNGEON_DISPLAY: // 使用副本显示的场景类型
        case SCENARIO_TYPE_BOOST_TUTORIAL: // 快速提升教程场景类型
            if (uint32 dungeonId = sLFGMgr->GetDungeon(groupGuid)) // lfg dungeons are rewarded through lfg // 通过LFG系统获取副本ID，LFG副本通过LFG系统发放奖励
            {
                // lfg dungeon that we are in is not current scenario // 检查当前LFG副本是否与场景匹配
                if (dungeonId != dungeonData->id) // 如果LFG副本ID与场景副本数据ID不匹配
                    return; // 不处理奖励，避免错误的奖励发放

                sLFGMgr->FinishDungeon(groupGuid, dungeonId); // 通知LFG管理器完成副本，触发LFG奖励系统
            }
            break;
        case SCENARIO_TYPE_CHALLENGE_MODE: // 挑战模式场景类型
            if (_challenge) // 如果存在挑战模式对象
                _challenge->Complete(); // 调用挑战模式完成方法，处理挑战模式特有的奖励和统计
            break;
        case SCENARIO_TYPE_LEGION_INVASION: /// The Broken Shore scenario. // 军团入侵场景类型（破碎海岸场景）
        default: // 默认情况和其他未处理的场景类型
            break; // 不执行额外的奖励后处理
    }
}

AchievementMgr<Scenario>& Scenario::GetAchievementMgr()
{
    return m_achievementMgr;
}

AchievementMgr<Scenario> const& Scenario::GetAchievementMgr() const
{
    return m_achievementMgr;
}

// 获取步骤状态函数 - 查询指定场景步骤的当前状态
ScenarioStepState Scenario::GetStepState(ScenarioStepEntry const* step)
{
    auto const itr = _stepStates.find(step); // 在步骤状态映射表中查找指定步骤
    if (itr != _stepStates.end()) // 如果找到了对应的步骤状态记录
        return itr->second; // 返回该步骤的状态（未开始/进行中/已完成）

    return SCENARIO_STEP_INVALID; // 如果未找到步骤状态记录，返回无效状态
}

// 获取奖励目标数据函数 - 构建发送给客户端的奖励目标信息列表
std::vector<WorldPackets::Scenario::BonusObjectiveData> Scenario::GetBonusObjectivesData()
{
    std::vector<WorldPackets::Scenario::BonusObjectiveData> bonusObjectivesData; // 创建奖励目标数据容器
    for (auto const& step : steps) // 遍历所有场景步骤
    {
        if (!step->IsBonusObjective()) // 检查步骤是否为奖励目标
            continue; // 跳过非奖励目标步骤

        // 验证步骤的标准树是否在成就管理器中存在
        if (sAchievementMgr->GetCriteriaTree(step->Criteriatreeid))
        {
            WorldPackets::Scenario::BonusObjectiveData bonusObjectiveData; // 创建奖励目标数据结构
            bonusObjectiveData.BonusObjectiveID = step->ID; // 设置奖励目标ID（对应步骤ID）
            bonusObjectiveData.ObjectiveComplete = GetStepState(step) == SCENARIO_STEP_DONE; // 检查奖励目标是否已完成
            bonusObjectivesData.push_back(bonusObjectiveData); // 将奖励目标数据添加到列表中
        }
    }

    return bonusObjectivesData; // 返回完整的奖励目标数据列表，用于客户端UI显示
}

// 发送步骤更新数据包函数 - 向客户端发送场景状态和进度信息
void Scenario::SendStepUpdate(Player* player, bool full)
{
    WorldPackets::Scenario::ScenarioState state; // 创建场景状态数据包结构
    state.BonusObjectives = GetBonusObjectivesData(); // 获取奖励目标数据列表，包含ID和完成状态
    state.ScenarioID = GetScenarioId(); // 设置场景ID，用于客户端识别当前场景
    state.CurrentStep = currentStep < steps.size() ? steps[currentStep]->ID : -1; // 设置当前步骤ID，如果步骤索引无效则设为-1
    state.ScenarioComplete = IsCompleted(false); // 设置场景完成状态（仅检查主要目标，不包括奖励目标）
    state.ActiveSteps = ActiveSteps; // 设置活跃步骤ID列表，客户端用于显示当前进行的步骤

    // 获取场景相关的法术数据，用于在特定步骤启用特殊技能
    std::vector<ScenarioSpellData> const* scSpells = sObjectMgr->GetScenarioSpells(GetScenarioId());
    if (scSpells) // 如果存在场景法术数据
    {
        // 遍历所有场景法术，查找当前步骤可用的法术
        for (std::vector<ScenarioSpellData>::const_iterator itr = scSpells->begin(); itr != scSpells->end(); ++itr)
        {
           // if ((*itr).StepId == state.ActiveSteps) // 原始注释的代码：检查法术是否对应活跃步骤
            if ((*itr).StepId == GetCurrentStep()) // 检查法术是否对应当前步骤
            {
                WorldPackets::Scenario::ScenarioState::ScenarioSpellUpdate spellUpdate; // 创建法术更新数据结构
                spellUpdate.Usable = true; // 设置法术为可用状态
                spellUpdate.SpellID = (*itr).Spells; // 设置法术ID
                state.Spells.emplace_back(spellUpdate); // 将法术更新数据添加到状态包中
            }
        }
    }

    // 如果是完整更新，包含成就进度信息
    if (full)
    {
        // 从成就管理器获取标准进度映射表
        CriteriaProgressMap const* progressMap = GetAchievementMgr().GetCriteriaProgressMap();
        if (!progressMap->empty()) // 如果进度映射表不为空
        {
            // 遍历所有标准进度记录
            for (CriteriaProgressMap::const_iterator itr = progressMap->begin(); itr != progressMap->end(); ++itr)
            {
                CriteriaProgress const& treeProgress = itr->second; // 获取标准进度数据
                CriteriaTreeEntry const* criteriaTreeEntry = sCriteriaTreeStore.LookupEntry(itr->first); // 从标准树存储中查找对应条目
                if (!criteriaTreeEntry) // 如果标准树条目不存在，跳过此进度记录
                    continue;

                WorldPackets::Achievement::CriteriaTreeProgress progress; // 创建标准树进度数据包结构
                progress.Id = criteriaTreeEntry->CriteriaID; // 设置标准ID（用于客户端匹配成就标准）
                progress.Quantity = treeProgress.Counter; // 设置进度数量（当前完成的数量）
                progress.Player = ObjectGuid::Create<HighGuid::Scenario>(0, GetScenarioId(), 1); // whats the fuck ? // 创建场景类型的GUID作为玩家标识（注释表示对此逻辑的疑惑）
                progress.Flags = 0; // 设置标志位为0（无特殊标志）
                progress.Date = time(nullptr) - treeProgress.date; // 计算从进度开始到现在的时间差
                progress.TimeFromStart = time(nullptr) - treeProgress.date; // 设置从开始时间的偏移量
                progress.TimeFromCreate = time(nullptr) - treeProgress.date; // 设置从创建时间的偏移量
                state.Progress.push_back(progress); // 将进度数据添加到状态包的进度列表中
            }
        }
    }

    // 根据参数决定发送方式：单播还是广播
    if (player) // 如果指定了特定玩家
        player->SendDirectMessage(state.Write()); // 直接向该玩家发送数据包
    else
        BroadCastPacket(state.Write()); // 广播给场景中的所有玩家

    // 如果是完整更新且存在挑战模式，发送挑战模式相关信息
    if (full && _challenge)
    {
        _challenge->SendChallengeModeStart(player); // 发送挑战模式开始信息（包含词缀、等级等）
        _challenge->SendStartElapsedTimer(player); // 发送挑战模式计时器信息（已用时间）
    }
}

// 发送场景完成数据包函数 - 向指定玩家发送场景完成状态信息
void Scenario::SendFinishPacket(Player * player)
{
    WorldPackets::Scenario::ScenarioState state; // 创建场景状态数据包结构
    state.BonusObjectives = GetBonusObjectivesData(); // 获取奖励目标数据，显示哪些奖励目标已完成
    state.ScenarioID = GetScenarioId(); // 设置场景ID，标识完成的场景
    state.CurrentStep = currentStep < steps.size() ? steps[currentStep]->ID : -1; // 设置当前步骤ID（通常是最后一步或无效值）
    state.ScenarioComplete = true; // 强制设置场景完成状态为true，覆盖实际计算结果
    state.ActiveSteps = ActiveSteps; // 设置活跃步骤列表（完成时可能为空或包含最后步骤）
    player->SendDirectMessage(state.Write()); // 直接向指定玩家发送场景完成数据包
}

// 发送标准更新数据包函数 - 向所有玩家广播成就标准进度更新
void Scenario::SendCriteriaUpdate(CriteriaProgress const* progress, uint32 timeElapsed /* = 0*/)
{
    //TC_LOG_DEBUG(LOG_FILTER_CHALLENGE, "Scenario::SendCriteriaUpdate criteria %u Counter %u", progress->criteriaTree->criteria, progress->Counter); // 调试日志：记录标准更新信息

    // 验证进度数据和标准树的有效性
    if (!progress || !progress->criteriaTree)
        return;

    WorldPackets::Scenario::ScenarioProgressUpdate update; // 创建场景进度更新数据包

    // 获取进度更新数据结构的引用，避免重复访问
    WorldPackets::Achievement::CriteriaTreeProgress& progressUpdate = update.Progress;
    progressUpdate.Id = progress->criteriaTree->CriteriaID; // 设置标准ID，客户端用于匹配对应的成就标准
    progressUpdate.Quantity = progress->Counter; // 设置当前进度数量（如击杀数、收集数等）
    progressUpdate.Player = progress->PlayerGUID; // 设置触发进度更新的玩家GUID
    progressUpdate.Flags = 0; // 设置标志位为0（无特殊标志）
    progressUpdate.Date = progress->date; // 设置进度更新的时间戳
    progressUpdate.TimeFromStart = timeElapsed; // 设置从场景开始到现在的经过时间
    progressUpdate.TimeFromCreate = timeElapsed; // 设置从进度创建到现在的经过时间
    BroadCastPacket(update.Write()); // 广播进度更新数据包给场景中的所有玩家
}

// 广播数据包函数 - 根据场景类型选择不同的广播机制
void Scenario::BroadCastPacket(const WorldPacket* data)
{
    // 优先处理户外PvP场景的广播
    if (m_outdoorPvp)
    {
        m_outdoorPvp->BroadcastPacketByZone(*data, m_outdoorPvpZone); // 向指定户外PvP区域内的所有玩家广播数据包
        return; // 户外PvP广播完成后直接返回，不执行后续的普通地图广播
    }

    // 处理普通副本场景的广播
    Map* map = sMapMgr->FindMap(dungeonData->map, instanceId); // 通过地图管理器查找对应的地图实例
    if (!map || map->IsMapUnload()) // 检查地图是否存在且未被卸载
        return; // 地图无效时直接返回，避免向无效地图发送数据包

    map->SendToPlayers(data); // 向地图上的所有玩家发送数据包
}

// 检查是否可以更新标准函数 - 递归检查标准树结构确定是否允许更新指定标准
bool Scenario::CanUpdateCriteria(uint32 criteriaId, uint32 recursTree /*=0*/) const
{
    // 获取标准树列表：如果提供了递归树ID则使用它，否则使用当前场景的标准树
    auto const& cTreeList = sDB2Manager.GetCriteriaTreeList(recursTree ? recursTree : currentTree);
    if (!cTreeList) // 如果标准树列表不存在，返回false表示不能更新
        return false;

    // 遍历标准树列表中的所有标准树条目
    for (std::vector<CriteriaTreeEntry const*>::const_iterator itr = cTreeList->begin(); itr != cTreeList->end(); ++itr)
    {
        if (CriteriaTreeEntry const* criteriaTree = *itr) // 验证标准树条目指针有效
        {
            if (criteriaTree->CriteriaID == 0) // 如果标准ID为0，表示这是一个父节点（容器节点）
            {
                // 递归检查子标准树，使用当前标准树的ID作为新的递归树ID
                if (CanUpdateCriteria(criteriaId, criteriaTree->ID))
                    return true; // 如果在子树中找到匹配的标准，返回true
            }
            else if (criteriaTree->ID == criteriaId) // 如果标准树ID与目标标准ID匹配
                return true; // 找到匹配的标准，返回true表示可以更新
        }
    }

    return false; // 遍历完所有标准树条目都没有找到匹配的标准，返回false
}

Challenge* Scenario::GetChallenge()
{
    return _challenge;
}

void Scenario::SetOutdoorPvP(OutdoorPvP * outdoor, uint32 zone)
{
    m_outdoorPvp = outdoor;
    m_outdoorPvpZone = zone;
}

void Scenario::UpdateAchievementCriteria(CriteriaTypes type, uint32 miscValue1 /*= 0*/, uint32 miscValue2 /*= 0*/, uint32 miscValue3 /*= 0*/, Unit* unit /*= NULL*/, Player* referencePlayer /*= NULL*/)
{
    AchievementCachePtr referenceCache = std::make_shared<AchievementCache>(referencePlayer, unit, type, miscValue1, miscValue2, miscValue3);

    GetAchievementMgr().UpdateAchievementCriteria(referenceCache);
}
